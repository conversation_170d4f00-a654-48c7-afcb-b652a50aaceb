/**
 * Diagnostic <PERSON>t for Castmate Codebase Indexing Issues
 * 
 * This script analyzes the Castmate project directory structure to identify
 * why certain files and directories were not included in the indexing process.
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { Dirent } from 'fs';

interface DiagnosticResult {
  totalFiles: number;
  foundFiles: string[];
  missingFiles: string[];
  skippedDirectories: string[];
  errorPaths: string[];
  filesByExtension: Record<string, number>;
  directoryStructure: Record<string, number>;
}

class CastmateDiagnosticTool {
  private defaultExcludePatterns = [
    'node_modules',
    '.git',
    '.next',
    'dist',
    'build',
    '.vscode',
    'coverage',
    '.nuxt',
    '.output',
    '__pycache__',
    '.env',
    '.env.local',
    '.env.production',
    '.env.development',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db',
    '.cache',
    'tmp',
    'temp'
  ];

  private defaultIncludeExtensions = [
    '.ts', '.tsx', '.js', '.jsx',
    '.py', '.java', '.cpp', '.c',
    '.cs', '.go', '.rs', '.php',
    '.rb', '.swift', '.kt', '.scala',
    '.md', '.txt', '.json', '.yaml', '.yml'
  ];

  // Expected missing files from the problem statement
  private expectedMissingDirectories = [
    'components/Agents',
    'components/scriptreaderAI',
    'components/VoiceAssistants'
  ];

  private expectedMissingApiEndpoints = [
    'app/api/processScriptfile',
    'app/api/processMessage',
    'app/api/selfTakeAudio',
    'app/api/selfTakeVideo',
    'app/api/searchChat',
    'app/api/session',
    'app/api/transcribeAudio',
    'app/api/user-agent',
    'app/api/validate-agent-config',
    'app/api/format-script',
    'app/api/format-script-fallback',
    'app/api/elevenlabs-webhook',
    'app/api/auth'
  ];

  private expectedMissingFiles = [
    'components/Header.tsx',
    'components/LoadingOverlay.tsx',
    'hooks/useUserAgent.ts',
    'hooks/useAgentConfigValidation.ts',
    'lib/fetchDocumentChunks.ts',
    'lib/fetchDocumentChunksByChunkIds.ts',
    'lib/fetchDocumentTitlesByCategory.tsx'
  ];

  async diagnoseCastmateProject(rootPath: string): Promise<DiagnosticResult> {
    console.log(`🔍 Starting diagnostic analysis of Castmate project at: ${rootPath}`);
    
    const result: DiagnosticResult = {
      totalFiles: 0,
      foundFiles: [],
      missingFiles: [],
      skippedDirectories: [],
      errorPaths: [],
      filesByExtension: {},
      directoryStructure: {}
    };

    const includeExtSet = new Set(this.defaultIncludeExtensions.map(ext => ext.toLowerCase()));

    const traverse = async (currentPath: string, currentRelativePath: string, depth: number = 0): Promise<void> => {
      let items: Dirent[];
      try {
        items = await fs.readdir(currentPath, { withFileTypes: true });
      } catch (error) {
        const errorMsg = `Cannot read directory: ${currentPath} - ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errorPaths.push(errorMsg);
        console.warn(`❌ ${errorMsg}`);
        return;
      }

      for (const item of items) {
        const itemName = item.name;
        const fullPath = path.join(currentPath, itemName);
        const relativePath = path.join(currentRelativePath, itemName);

        if (item.isDirectory()) {
          // Track directory structure
          const dirKey = `depth_${depth}`;
          result.directoryStructure[dirKey] = (result.directoryStructure[dirKey] || 0) + 1;

          // Check if this directory should be excluded
          const shouldExclude = this.defaultExcludePatterns.some(pattern => {
            const normalizedPattern = pattern.toLowerCase();
            const normalizedRelativePath = relativePath.toLowerCase();
            return normalizedRelativePath === normalizedPattern || 
                   normalizedRelativePath.startsWith(normalizedPattern + path.sep) ||
                   normalizedRelativePath.includes(path.sep + normalizedPattern + path.sep) ||
                   normalizedRelativePath.endsWith(path.sep + normalizedPattern);
          });

          if (shouldExclude) {
            result.skippedDirectories.push(relativePath);
            console.log(`⏭️  Skipping excluded directory: ${relativePath}`);
            continue;
          }

          // Check if this is one of the expected missing directories
          const isExpectedMissingDir = this.expectedMissingDirectories.some(expectedDir => 
            relativePath.toLowerCase().includes(expectedDir.toLowerCase())
          );

          if (isExpectedMissingDir) {
            console.log(`🎯 Found expected missing directory: ${relativePath}`);
          }

          await traverse(fullPath, relativePath, depth + 1);
        } else if (item.isFile()) {
          result.totalFiles++;
          const ext = path.extname(itemName).toLowerCase();
          
          // Track file extensions
          result.filesByExtension[ext] = (result.filesByExtension[ext] || 0) + 1;

          if (includeExtSet.has(ext)) {
            result.foundFiles.push(relativePath);
            console.log(`📄 Found indexable file: ${relativePath}`);

            // Check if this is one of the expected missing files
            const isExpectedMissingFile = this.expectedMissingFiles.some(expectedFile => 
              relativePath.toLowerCase().includes(expectedFile.toLowerCase())
            );

            if (isExpectedMissingFile) {
              console.log(`🎯 Found expected missing file: ${relativePath}`);
            }
          }
        }
      }
    };

    const normalizedRootPath = path.resolve(rootPath);
    await traverse(normalizedRootPath, '', 0);

    // Check for missing expected files and directories
    for (const expectedFile of this.expectedMissingFiles) {
      const found = result.foundFiles.some(file => 
        file.toLowerCase().includes(expectedFile.toLowerCase())
      );
      if (!found) {
        result.missingFiles.push(expectedFile);
      }
    }

    for (const expectedDir of this.expectedMissingDirectories) {
      const found = result.foundFiles.some(file => 
        file.toLowerCase().includes(expectedDir.toLowerCase())
      );
      if (!found) {
        console.log(`❌ Missing expected directory: ${expectedDir}`);
      }
    }

    return result;
  }

  printDiagnosticReport(result: DiagnosticResult): void {
    console.log('\n📊 CASTMATE INDEXING DIAGNOSTIC REPORT');
    console.log('=====================================');
    
    console.log(`\n📈 Overall Statistics:`);
    console.log(`   - Total files found: ${result.totalFiles}`);
    console.log(`   - Indexable files found: ${result.foundFiles.length}`);
    console.log(`   - Missing expected files: ${result.missingFiles.length}`);
    console.log(`   - Skipped directories: ${result.skippedDirectories.length}`);
    console.log(`   - Error paths: ${result.errorPaths.length}`);

    console.log(`\n📁 File Extensions Distribution:`);
    Object.entries(result.filesByExtension)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 15)
      .forEach(([ext, count]) => {
        const isIndexable = this.defaultIncludeExtensions.includes(ext);
        console.log(`   ${isIndexable ? '✅' : '❌'} ${ext}: ${count} files`);
      });

    console.log(`\n📂 Directory Structure by Depth:`);
    Object.entries(result.directoryStructure)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([depth, count]) => {
        console.log(`   ${depth}: ${count} directories`);
      });

    if (result.missingFiles.length > 0) {
      console.log(`\n❌ Missing Expected Files:`);
      result.missingFiles.forEach(file => console.log(`   - ${file}`));
    }

    if (result.errorPaths.length > 0) {
      console.log(`\n🚨 Error Paths:`);
      result.errorPaths.forEach(error => console.log(`   - ${error}`));
    }

    if (result.skippedDirectories.length > 0) {
      console.log(`\n⏭️  Skipped Directories (first 10):`);
      result.skippedDirectories.slice(0, 10).forEach(dir => console.log(`   - ${dir}`));
    }
  }
}

// Main execution function
async function main() {
  const diagnosticTool = new CastmateDiagnosticTool();
  
  // You can modify this path to point to your actual Castmate project
  const castmateProjectPath = process.argv[2] || 'D:\\Ike-FoodKhare Backups\\CastMate - 030725 CastmateElevenLab ConsoleLogs';
  
  try {
    console.log(`🚀 Running Castmate indexing diagnostic...`);
    const result = await diagnosticTool.diagnoseCastmateProject(castmateProjectPath);
    diagnosticTool.printDiagnosticReport(result);
    
    console.log('\n✅ Diagnostic complete!');
    console.log('\n💡 Recommendations:');
    console.log('   1. Check if the root path is correct');
    console.log('   2. Verify file permissions for any error paths');
    console.log('   3. Consider adjusting exclude patterns if needed');
    console.log('   4. Run indexing with verbose=true for detailed logging');
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
}

// Run the diagnostic if this script is executed directly
if (require.main === module) {
  main();
}

export { CastmateDiagnosticTool };
